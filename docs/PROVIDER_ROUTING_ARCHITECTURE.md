# Provider-Specific Routing Architecture

## Overview

This document describes the new provider-specific routing architecture implemented for the cloud inventory application. The system now supports separate endpoints for different cloud providers while maintaining shared UI components and allowing for future customization.

## Architecture Components

### 1. Route Structure

The application now supports the following routing structure:

- **Root Route (`/`)**: Automatically redirects to `/gcp` as the default provider
- **AWS Routes (`/aws/*`)**: All AWS-specific functionality
- **GCP Routes (`/gcp/*`)**: All GCP-specific functionality

### 2. Blueprint Organization

#### Main Blueprint (`app/routes/main.py`)
- Handles the root route redirect
- Maintains existing shared functionality like file downloads and video data

#### Provider-Specific Blueprints
- **AWS Blueprint** (`app/routes/aws.py`): Handles all `/aws/*` routes
- **GCP Blueprint** (`app/routes/gcp.py`): Handles all `/gcp/*` routes

Each provider blueprint includes:
- Home page (`/`)
- Logs page (`/logs`)
- Logging view (`/logging`)
- Video page (`/video`)
- Video data API (`/video/data`)
- File download (`/dfile`)

### 3. Template Structure

```
templates/
├── shared/
│   ├── provider_base.html      # Base template for all providers
│   └── macros.html            # Reusable UI components
├── providers/
│   ├── aws/
│   │   ├── index.html         # AWS-specific home page
│   │   ├── log.html           # AWS-specific log page
│   │   ├── logging.html       # AWS-specific logging page
│   │   └── video.html         # AWS-specific video page
│   └── gcp/
│       ├── index.html         # GCP-specific home page
│       ├── log.html           # GCP-specific log page
│       ├── logging.html       # GCP-specific logging page
│       └── video.html         # GCP-specific video page
└── [existing templates...]
```

### 4. Shared Components

#### Provider Base Template (`templates/shared/provider_base.html`)
- Extends the main `base.html`
- Provides blocks for provider-specific content:
  - `provider_tabs`: Navigation tabs
  - `provider_content`: Main content area
  - `compliance_tabs`: Compliance navigation
  - `compliance_content`: Compliance content
  - `additional_content`: Extra provider-specific content

#### Macros (`templates/shared/macros.html`)
- `render_tab_button()`: Creates navigation tab buttons
- `render_tab_content()`: Creates complete tab content sections
- `render_form_section()`: Creates form sections with fields

## Current Implementation

### Phase 1: Identical UI
Both `/aws` and `/gcp` endpoints currently serve nearly identical content with the following differences:
- **Tab ordering**: Each provider prioritizes its own services first
- **Provider context**: Templates receive `provider` and `provider_name` variables
- **Future-ready structure**: Architecture supports easy customization

### Default Behavior
- Visiting `/` automatically redirects to `/gcp`
- Both endpoints are fully functional
- All existing API endpoints continue to work

## Adding Provider-Specific Customizations

### 1. Customizing Templates

To add provider-specific features to a template:

```html
<!-- In templates/providers/aws/index.html -->
{% extends 'shared/provider_base.html' %}

{% block provider_content %}
<!-- AWS-specific content here -->
{% if provider == 'aws' %}
    <div class="aws-specific-feature">
        <!-- AWS-only content -->
    </div>
{% endif %}
{% endblock %}
```

### 2. Adding New Routes

To add a new provider-specific route:

```python
# In app/routes/aws.py
@aws_bp.route('/new-feature')
def aws_new_feature():
    return render_template('providers/aws/new_feature.html', 
                         provider='aws')
```

### 3. Provider-Specific Styling

Add provider-specific CSS classes or styles:

```html
<!-- In provider templates -->
<div class="provider-{{ provider }}">
    <!-- Content with provider-specific styling -->
</div>
```

### 4. Conditional Content

Use the `provider` variable for conditional content:

```html
{% if provider == 'aws' %}
    <!-- AWS-specific instructions -->
{% elif provider == 'gcp' %}
    <!-- GCP-specific instructions -->
{% endif %}
```

## Testing

The routing implementation has been tested and verified:

1. **Root Redirect**: `/` correctly redirects to `/gcp/`
2. **Provider Routes**: Both `/aws/` and `/gcp/` serve content successfully
3. **Sub-routes**: All sub-routes (logs, video, etc.) work for both providers
4. **API Compatibility**: Existing API endpoints remain functional

## Future Enhancements

### Planned Customizations
1. **Provider-specific branding**: Colors, logos, and styling
2. **Feature availability**: Show/hide features based on provider
3. **Provider-specific documentation**: Tailored instructions and examples
4. **Custom workflows**: Provider-specific user flows and processes

### Adding New Providers
To add a new cloud provider (e.g., Azure):

1. Create new blueprint: `app/routes/azure.py`
2. Create template directory: `templates/providers/azure/`
3. Register blueprint in `app/__init__.py`
4. Implement provider-specific templates extending `shared/provider_base.html`

## Migration Notes

- **Backward Compatibility**: All existing functionality remains available
- **URL Changes**: Users accessing the root URL will be redirected to `/gcp`
- **API Endpoints**: No changes to existing API endpoints
- **Templates**: Original templates remain unchanged and functional

## Configuration

The provider routing is configured in `app/__init__.py`:

```python
from .routes.aws import aws_bp
from .routes.gcp import gcp_bp

app.register_blueprint(aws_bp)
app.register_blueprint(gcp_bp)
```

## Quick Start Guide

### Testing the New Routes

1. **Start the application**:
   ```bash
   python main.py
   ```

2. **Test the routes**:
   - Visit `http://localhost:8080/` - should redirect to `/gcp`
   - Visit `http://localhost:8080/aws/` - AWS provider interface
   - Visit `http://localhost:8080/gcp/` - GCP provider interface

3. **Verify functionality**:
   - Both endpoints should show similar UI with provider-specific tab ordering
   - All forms and features should work identically
   - API endpoints remain unchanged

### Making Your First Customization

1. **Add AWS-specific content**:
   ```html
   <!-- In templates/providers/aws/index.html -->
   {% block additional_content %}
   <div class="aws-banner">
       <h2>Welcome to AWS Cloud Tools</h2>
       <p>Specialized tools for Amazon Web Services</p>
   </div>
   {% endblock %}
   ```

2. **Add provider-specific styling**:
   ```css
   /* In static/styles/ */
   .provider-aws .aws-banner {
       background-color: #ff9900;
       color: white;
       padding: 20px;
   }
   ```

## Troubleshooting

### Common Issues

1. **Template Not Found**: Ensure provider-specific templates exist or extend base templates
2. **Route Conflicts**: Check blueprint URL prefixes don't conflict
3. **Static Assets**: Verify static file paths work for all providers

### Debug Mode

Enable Flask debug mode to see detailed error messages:

```bash
export FLASK_DEBUG=1
python main.py
```

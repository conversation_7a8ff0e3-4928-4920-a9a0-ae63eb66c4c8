import os
from flask import Blueprint, render_template, request, jsonify, send_file
from ..utils import get_path
from ai.config import ALLOWED_USERS
from video.excel import read_google_sheet

gcp_bp = Blueprint('gcp', __name__, url_prefix='/gcp')

@gcp_bp.route('/')
def gcp_home():
    """GCP provider home page"""
    user_email = request.headers.get('X-Goog-Authenticated-User-Email', 'Unknown').split(':')[-1]
    user_is_allowed = user_email in ALLOWED_USERS
    return render_template('providers/gcp/index.html', 
                         user_is_allowed=user_is_allowed, 
                         provider='gcp',
                         provider_name='GCP')

@gcp_bp.route('/logs')
def gcp_logs_page():
    """GCP provider logs page"""
    return render_template('providers/gcp/log.html', provider='gcp')

@gcp_bp.route('/logging')
def gcp_log_view():
    """GCP provider logging view"""
    log_file_path = get_path("app.log")
    html_log_path = get_path("templates/log.html")
    try:
        with open(log_file_path, "r") as f:
            server_content = f.read()
    except FileNotFoundError:
        server_content = "Log file not found."
    try:
        with open(html_log_path, "r") as f:
            process_content = f.read()
    except FileNotFoundError:
        process_content = "Process log file not found."
    return render_template('providers/gcp/logging.html', 
                         server_content=server_content, 
                         process_content=process_content,
                         provider='gcp')

@gcp_bp.route('/video')
def gcp_video_page():
    """GCP provider video page"""
    return render_template('providers/gcp/video.html', provider='gcp')

@gcp_bp.route('/video/data')
def gcp_video_data():
    """GCP provider video data"""
    sheet_url = 'https://docs.google.com/spreadsheets/d/1CQZynGXu96PyhNUwqsXiKbGRkpUvqNTRSnNCSCf8QEk/edit'
    return jsonify(read_google_sheet(sheet_url, 'data'))

@gcp_bp.route('/dfile')
def gcp_download_file():
    """GCP provider file download"""
    file_path = request.args.get('path')
    if file_path and os.path.exists(file_path) and file_path.startswith(get_path('output')):
        return send_file(file_path, as_attachment=True)
    return "Error: Invalid file path or file not found.", 404

import os
from flask import Blueprint, render_template, request, jsonify, send_file
from ..utils import get_path
from ai.config import ALLOWED_USERS
from video.excel import read_google_sheet

aws_bp = Blueprint('aws', __name__, url_prefix='/aws')

@aws_bp.route('/')
def aws_home():
    """AWS provider home page"""
    user_email = request.headers.get('X-Goog-Authenticated-User-Email', 'Unknown').split(':')[-1]
    user_is_allowed = user_email in ALLOWED_USERS
    return render_template('providers/aws/index.html', 
                         user_is_allowed=user_is_allowed, 
                         provider='aws',
                         provider_name='AWS')

@aws_bp.route('/logs')
def aws_logs_page():
    """AWS provider logs page"""
    return render_template('providers/aws/log.html', provider='aws')

@aws_bp.route('/logging')
def aws_log_view():
    """AWS provider logging view"""
    log_file_path = get_path("app.log")
    html_log_path = get_path("templates/log.html")
    try:
        with open(log_file_path, "r") as f:
            server_content = f.read()
    except FileNotFoundError:
        server_content = "Log file not found."
    try:
        with open(html_log_path, "r") as f:
            process_content = f.read()
    except FileNotFoundError:
        process_content = "Process log file not found."
    return render_template('providers/aws/logging.html', 
                         server_content=server_content, 
                         process_content=process_content,
                         provider='aws')

@aws_bp.route('/video')
def aws_video_page():
    """AWS provider video page"""
    return render_template('providers/aws/video.html', provider='aws')

@aws_bp.route('/video/data')
def aws_video_data():
    """AWS provider video data"""
    sheet_url = 'https://docs.google.com/spreadsheets/d/1CQZynGXu96PyhNUwqsXiKbGRkpUvqNTRSnNCSCf8QEk/edit'
    return jsonify(read_google_sheet(sheet_url, 'data'))

@aws_bp.route('/dfile')
def aws_download_file():
    """AWS provider file download"""
    file_path = request.args.get('path')
    if file_path and os.path.exists(file_path) and file_path.startswith(get_path('output')):
        return send_file(file_path, as_attachment=True)
    return "Error: Invalid file path or file not found.", 404

{% macro render_tab_button(tab_id, image_src, tab_name, active=false) %}
<li>
	<a {% if active %}class="active"{% endif %} data-toggle="tab" href="#{{ tab_id }}">
		<img src="{{ image_src }}" />
		<span>{{ tab_name }}</span>
	</a>
</li>
{% endmacro %}

{% macro render_form_section(form_id, action_url, call_value, form_fields, button_text, button_id, color_class="tilt-btn") %}
<div class="form-content">
	<div class="card-body" style="border-radius:5px; float: left; width : 100%; height : 500px;">
		<form id="{{ form_id }}" action="{{ action_url }}" method="post" enctype="multipart/form-data"
			onsubmit="addUUID(this);  revealText(this, '{{ color_class.replace('-btn', '') }}','');">
			<div class="form-group" style="padding-right:0px;margin-top: 10px;">
				<input type="hidden" id="call" name="call" value="{{ call_value }}">
				
				{% for field in form_fields %}
				{% if field.type == 'text' %}
				<span class="span-form-group{{ '-' + field.class_suffix if field.class_suffix else '' }}">{{ field.label }}</span>
				<input type="text" placeholder="{{ field.placeholder }}" 
					   class="form-group-class{{ '-' + field.class_suffix if field.class_suffix else '' }}" 
					   id="{{ field.id }}" name="{{ field.name }}" 
					   style="border-radius:5px;" 
					   {% if field.required %}required{% endif %}><br><br><br>
				{% elif field.type == 'textarea' %}
				<span class="span-form-group{{ '-' + field.class_suffix if field.class_suffix else '' }}">{{ field.label }}</span>
				<textarea placeholder="{{ field.placeholder }}" 
						  class="form-group-class{{ '-' + field.class_suffix if field.class_suffix else '' }}" 
						  id="{{ field.id }}" name="{{ field.name }}" 
						  style="border-radius:5px;" rows="{{ field.rows or 6 }}"
						  {% if field.required %}required{% endif %}></textarea><br>
				{% elif field.type == 'file' %}
				<span class="span-form-group">{{ field.label }}</span>
				<input type="file" id="{{ field.id }}" name="{{ field.name }}" 
					   accept="{{ field.accept }}" {% if field.required %}required{% endif %}><br><br><br>
				{% elif field.type == 'select' %}
				<span class="span-form-group">{{ field.label }}</span>
				<select id="{{ field.id }}" class="form-group-class" 
						style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;" 
						name="{{ field.name }}">
					<option value="" disabled selected>{{ field.placeholder }}</option>
					{% for option in field.options %}
					<option value="{{ option.value }}">{{ option.text }}</option>
					{% endfor %}
				</select><br><br><br>
				{% elif field.type == 'hidden' %}
				<input type="hidden" name="{{ field.name }}" id="{{ field.id }}" value="{{ field.value }}">
				{% elif field.type == 'toggle' %}
				<div style="display: flex; align-items: center; gap: 10px; margin-top: 10px; margin-left: 4px;">
					<span class="span-form-group" style="position: relative; margin: 0; padding-right: 0;">{{ field.label }}</span>
					<label class="switch">
						<input type="checkbox" id="{{ field.id }}" name="{{ field.name }}" value="0" onchange="{{ field.onchange }}">
						<span class="slider round"></span>
					</label>
					<span id="{{ field.toggle_label_id }}">No</span>
				</div>
				{% endif %}
				{% endfor %}

				{% if user_is_allowed is defined %}
				<div class="slider-container">
					<span>AI Summary : </span>
					<label class="switch">
						{% if user_is_allowed %}
						<input type="checkbox" data-target="ai_summary" id="ai_summary" name="ai_summary">
						<span class="slider"></span>
						{% else %}
						<input type="checkbox" data-target="ai_summary" id="ai_summary" name="ai_summary" disabled>
						<span class="slider"></span>
					</label>
					<span class="text-danger" style="margin-left: 10px;">No Access</span>
					{% endif %}
				</div>
				{% endif %}

				<button id="{{ button_id }}" type="submit" class="btn arrow-btn {{ color_class }}"
					style="border-radius:5px; margin-left: -14px; margin-bottom: 78px;"
					{% if button_id == 'gcpin' %}disabled{% endif %}>{{ button_text }}</button>
			</div>
		</form>
		<div id="text-block-container-{{ color_class.replace('-btn', '') }}" style="filter:none"></div>
	</div>
</div>
{% endmacro %}

{% macro render_tab_content(tab_id, title, subtitle, requirements, steps, output_link, output_image, video_src, what_next, form_config, active=false) %}
<div id="{{ tab_id }}" class="tab-pane fade{% if active %} in active show{% endif %}">
	<div class="tab-body">
		<div class="content">
			<h3>
				{{ title }}
				<span class="text" style="color: {{ subtitle.color }}">{{ subtitle.text }}</span>
			</h3>
			<div>
				<div class="tab">
					<button class="tablinks" onclick="openCity(event, '{{ tab_id }}_req')">Requirements</button>
					<div id="{{ tab_id }}_req" class="tabcontent">
						{{ requirements | safe }}
					</div>
					<button class="tablinks" onclick="openCity(event, '{{ tab_id }}_steps')">Steps</button>
					<div id="{{ tab_id }}_steps" class="tabcontent">
						{{ steps | safe }}
					</div>
					<button class="tablinks" onclick="openCity(event, '{{ tab_id }}_out')">Output</button>
					<div id="{{ tab_id }}_out" class="tabcontent">
						<a href="{{ output_link }}" target="_blank">Sample Output</a>
						<div class="img-div">
							<img src="{{ output_image }}" style="margin-left: 0%; margin-top: -8%" width="100%" height="80%">
						</div>
					</div>
					<button class="tablinks" onclick="openCity(event, '{{ tab_id }}_vid')">Video</button>
					<div id="{{ tab_id }}_vid" class="tabcontent">
						{% if video_src %}
						<iframe src="{{ video_src }}" width="690" height="420" allow="autoplay; fullscreen;"></iframe>
						{% else %}
						Coming soon...
						{% endif %}
					</div>
					<button class="tablinks" onclick="openCity(event, '{{ tab_id }}_wn')">What Next?</button>
					<div id="{{ tab_id }}_wn" class="tabcontent">
						{{ what_next | safe }}
					</div>
				</div>
				
				{% if form_config %}
				{{ render_form_section(
					form_config.form_id,
					form_config.action_url,
					form_config.call_value,
					form_config.fields,
					form_config.button_text,
					form_config.button_id,
					form_config.color_class
				) }}
				{% endif %}
			</div>
		</div>
	</div>
</div>
{% endmacro %}

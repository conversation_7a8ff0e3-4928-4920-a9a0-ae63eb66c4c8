{% extends 'base.html' %}

{% block main %}
<!--inventory page division-->
<div id="div1" class="tab-pane fade in active show" xmlns="http://www.w3.org/1999/html">
	<div class="nav-container wow fadeInUp">
		<ul class="nav nav-tabs">
			{% block provider_tabs %}
			<!-- Provider-specific tabs will be defined in child templates -->
			{% endblock %}
		</ul>
	</div>
	<div class="container">
		<div class="tab-content section-padding wow fadeInUp">
			{% block provider_content %}
			<!-- Provider-specific content will be defined in child templates -->
			{% endblock %}
		</div>
	</div>
</div>

<!--Compliance page division-->
<div id="div2" class="hidden">
	<div class="nav-container wow fadeInUp">
		<ul class="nav nav-tabs">
			{% block compliance_tabs %}
			<!-- Provider-specific compliance tabs will be defined in child templates -->
			{% endblock %}
		</ul>
	</div>

	<div class="container">
		<div class="tab-content section-padding wow fadeInUp">
			{% block compliance_content %}
			<!-- Provider-specific compliance content will be defined in child templates -->
			{% endblock %}
		</div>
	</div>
</div>

{% block additional_content %}
<!-- Additional provider-specific content can be added here -->
{% endblock %}

{% endblock %}

{% extends 'shared/provider_base.html' %}
{% from 'shared/macros.html' import render_tab_button, render_tab_content %}

{% block provider_tabs %}
{{ render_tab_button('aws_inventory', './static/assets/images/awsinv1.png', 'AWS Inventory', active=true) }}
{{ render_tab_button('eks', './static/assets/images/eksinv.png', 'EKS Inventory') }}
{{ render_tab_button('gcp_inventory', './static/assets/images/azinv1.png', 'GCP Inventory') }}
{{ render_tab_button('gke_dragon', './static/assets/images/google-gke.svg', 'GKE Inventory') }}
{{ render_tab_button('ari', './static/assets/images/inv1.png', 'Azure Inventory') }}
{{ render_tab_button('oci_inv', './static/assets/images/oci_inv1.png', 'Oracle Inventory') }}
{% endblock %}

{% block provider_content %}
<!-- AWS Inventory Tab -->
{{ render_tab_content(
    'aws_inventory',
    'Generate AWS Inventory Workbook for an organisation',
    {'text': 'by providing ARN', 'color': '#29c8e1'},
    '<p>1. Cloud Run Google Service Account <span class="text" style="color: blue"><br><EMAIL></span> requires AWS IAM Policy <span class="text" style="color: black; font-weight: bold">arn:aws:iam::aws:policy/ReadOnlyAccess</span> on AWS project.</p>',
    '<p>1. Download <a href="https://storage.googleapis.com/hypatia-public-artifacts/hypatia-aws-eks-cfn.yaml" target="_blank">AWS Cloudformation file</a> and use the <a href="https://docs.google.com/document/d/1NtyMtZHWPc5eaZn5fcXBqYEHDCSyd87NrDFHgsAT6GY/view" target="_blank">instructions</a> or <a href="https://storage.googleapis.com/hypatia-public-artifacts/pdfs/aws-instructions-new.pdf" target="_blank">instructions.pdf</a>.<br>2. The Stack should create a IAM Role and will output its ARN. <br>3. You need to supply the ARNs in the form.<br>4. You can provide comma seperated regions. It is optional.<br>5. Click Generate.<br></p>',
    'https://docs.google.com/spreadsheets/d/1tGel4HcGTm5KTpiTSB5Ain4GDZP3687dGzwff_WP1C0/view?usp=drive_link',
    './static/assets/images/awsinvent1.png',
    'https://drive.google.com/file/d/12PsnDsvWwl8ArrsXqgoH6wul35J78DHi/preview',
    '<p>No further action is required.</p>',
    {
        'form_id': 'form-tilt1',
        'action_url': 'inventory',
        'call_value': 'aws',
        'fields': [
            {'type': 'text', 'label': 'ARN', 'placeholder': 'Enter ARN', 'id': 'arn', 'name': 'arn', 'required': true},
            {'type': 'text', 'label': 'Regions', 'placeholder': '*', 'id': 'regions', 'name': 'regions'},
            {'type': 'hidden', 'name': 'aws-target', 'id': 'aws-target', 'value': '0'},
            {'type': 'toggle', 'label': 'Target Columns', 'id': 'aws-target-checkbox', 'name': 'aws-target-checkbox', 'onchange': 'updateAwsTargetValue(this)', 'toggle_label_id': 'toggle-label-aws'}
        ],
        'button_text': 'Generate',
        'button_id': 'awsin',
        'color_class': 'tilt-btn'
    },
    active=true
) }}

<!-- EKS Inventory Tab -->
{{ render_tab_content(
    'eks',
    'Generate EKS Inventory Report',
    {'text': 'by entering the AWS Region', 'color': '#F08000'},
    '<p>1. Cloud Run Google Service Account <br><span class="text" style="color: blue"><EMAIL></span> requires AWS managed IAM Policy <span class="text" style="color: black; font-weight: bold">arn:aws:iam::aws:policy/ReadOnlyAccess</span> and custom IAM Policy <span class="text" style="color: black; font-weight: bold">Assume role</span>, <span class="text" style="color: black; font-weight: bold">EKS read only</span> on AWS project.<br></p>',
    '<p>1. Download <a href="https://storage.googleapis.com/hypatia-public-artifacts/hydra-aws-eks-cfn.yaml" target="_blank">AWS Cloudformation file</a> and use the <a href="https://docs.google.com/document/d/1NtyMtZHWPc5eaZn5fcXBqYEHDCSyd87NrDFHgsAT6GY/view" target="_blank">instructions</a> or <a href="https://storage.googleapis.com/hypatia-public-artifacts/pdfs/aws-instructions-new.pdf" target="_blank">instructions.pdf</a>.<br>2. The Stack should create a IAM Role and will output its ARN. <br>3. Enter the ARN.<br>4. Click Generate.<br></p>',
    'https://docs.google.com/spreadsheets/d/1swY9IdcqQKgPcW3Tjer7EzAIRfSq5Et3/view?usp=drive_link&ouid=111967750052980188249&rtpof=true&sd=true',
    './static/assets/images/eksinventory.png',
    'https://drive.google.com/file/d/1k80p14Rm_FpKS7uYKTGVvTyYCDG-gT99/preview',
    '<p>No further action is required.</p>',
    {
        'form_id': 'form-orange1',
        'action_url': 'kube',
        'call_value': 'aws',
        'fields': [
            {'type': 'text', 'label': 'ARN', 'placeholder': 'Enter ARN', 'id': 'arn', 'name': 'arn', 'required': true}
        ],
        'button_text': 'Generate',
        'button_id': 'awske',
        'color_class': 'purple-btn'
    }
) }}

<!-- Include other cloud provider tabs for comparison/reference -->
<!-- GCP Inventory Tab (for reference) -->
{{ render_tab_content(
    'gcp_inventory',
    'Generate GCP Inventory Workbook for an organisation',
    {'text': 'by giving org id', 'color': '#29c8e1'},
    '<p>1. Needs <span class="text" style="color: black; font-weight: bold"> Browser </span>and<span class="text" style="color: black; font-weight: bold"> Viewer </span> access at Organization Level for Cloud Run Service Account <span class="text" style="color: blue"><EMAIL></span><br>2. Will also work with <span class="text" style="color: black; font-weight: bold"> Viewer </span> access at Project level when projects have been selected.<br></p>',
    '<p>1. Fill in at least one field based on your requirements.<br>2. Enter GCP Organization ID to create an inventory for the whole organization.<br>3. Enter Folder IDs (comma-separated) to create an inventory for specific folders.<br>4. Enter Project IDs (comma-separated) to create an inventory for specific projects.<br>5. To include Redis, provide comma separated regions. Default region is us-central1.</p>',
    'https://docs.google.com/spreadsheets/d/1_D9pkJiBefJ1MXF9KGN97sz_h1qmYFT-/view?usp=sharing&ouid=105634715941788046732&rtpof=true&sd=true',
    './static/assets/images/gcpinvent1.png',
    'https://drive.google.com/file/d/1NZ96Cgwku385dW0A8MSAssljRudJH3n3/preview',
    '<p>No further action is required.</p>',
    {
        'form_id': 'form-tilt',
        'action_url': 'inventory',
        'call_value': 'gcp',
        'fields': [
            {'type': 'text', 'label': 'Org ID', 'placeholder': 'Enter Org Id', 'id': 'orgid', 'name': 'orgid', 'class_suffix': 'orgid'},
            {'type': 'text', 'label': 'Folder ID', 'placeholder': 'Folderid', 'id': 'folderids', 'name': 'folderids', 'class_suffix': 'projids'},
            {'type': 'text', 'label': 'Allowed Project IDs', 'placeholder': 'Proj1,Proj2,Proj3', 'id': 'projids', 'name': 'projids', 'class_suffix': 'projids'},
            {'type': 'text', 'label': 'Redis/Memcache Regions', 'placeholder': 'us-central1,us-east1...', 'id': 'redis-reg', 'name': 'redis-reg', 'class_suffix': 'projids'}
        ],
        'button_text': 'Generate',
        'button_id': 'gcpin',
        'color_class': 'tilt-btn'
    }
) }}
{% endblock %}

{% block compliance_tabs %}
{{ render_tab_button('aws_compliance', './static/assets/images/awslogo1.png', 'AWS Compliance', active=true) }}
{{ render_tab_button('gcp_compliance', './static/assets/images/gcpicon.png', 'GCP Compliance') }}
{{ render_tab_button('azure_compliance', './static/assets/images/azureicon.png', 'Azure Compliance') }}
{{ render_tab_button('firewall_summary', './static/assets/images/firewall_logs_logo.png', 'Firewall Summary') }}
{{ render_tab_button('vpcsc', './static/assets/images/vpcsc.png', 'VPC SC Rules') }}
{% endblock %}

{% block compliance_content %}
<!-- AWS Compliance content will be added here -->
<div id="aws_compliance" class="tab-pane fade in active show">
    <div class="tab-body">
        <div class="content">
            <h3>AWS Compliance features coming soon...</h3>
        </div>
    </div>
</div>
{% endblock %}

# Hypatia Documentation

## Overview
This project is a Flask-based backend application that provides various functionalities, including inventory management, compliance checks, migration processes, and Terraform configuration generation. It interacts with external services such as Google Cloud and AWS, and it is containerized using Docker for easy deployment.

## New Provider-Specific Routing

The application now supports provider-specific endpoints for better organization and future customization:

- **Root URL (`/`)**: Automatically redirects to `/gcp` (default provider)
- **AWS Interface (`/aws`)**: Dedicated interface for AWS cloud services
- **GCP Interface (`/gcp`)**: Dedicated interface for Google Cloud Platform services

Both interfaces currently provide identical functionality but are structured to allow for provider-specific customizations in the future. See [Provider Routing Architecture](docs/PROVIDER_ROUTING_ARCHITECTURE.md) for detailed documentation.

## Installation

### Prerequisites
Ensure you have the following installed:
- Python 3.x
- Docker (if running in a container)
- Pip (Python package manager)

### Setting up Locally
1. Clone the repository:
   ```sh
   git clone <repository-url>
   cd <repository-folder>
   ```
2. Install dependencies:
   ```sh
   pip install -r requirements.txt
   ```
3. Set up environment variables:
   ```sh
   export SECRET_KEY="your_secret_key"
   export PORT=5000
   ```
4. Run the application:
   ```sh
   python main.py
   ```

## Running with Docker

### Build and Run
1. Build the Docker image:
   ```sh
   docker build -t flask-app .
   ```
2. Run the container:
   ```sh
   docker run -d -p 5000:80 -e SECRET_KEY="your_secret_key" --name flask-app flask-app
   ```

## API Endpoints

### Authentication
- **Authorization**: Uses Google IAP JWT assertion.
- **Header Required**: `X-Goog-Iap-Jwt-Assertion`

### Endpoints Overview

| Endpoint        | Method | Description |
| -------------- | ------ |-------------|
| `/move_val`    | POST   | Validates migration readiness using VM metadata |
| `/m2vm`        | POST   | Generates M2VM migration plans based on input files |
| `/gta_m2vm`    | POST   | Processes GTA M2VM migration mappings |
| `/vmware_m2vm` | POST   | Generates VMware migration reports |
| `/buildsheet`  | POST   | Generates cloud infrastructure build sheets |
| `/gcloudcmd`   | POST   | Generates Google Cloud deployment commands |
| `/xl_to_tfvars`| POST   | Converts Excel configuration files into Terraform variables |
| `/compliance`  | POST   | Runs compliance checks for AWS, Azure, GCP, and VPCSC |
| `/stratozone`  | POST   | Generates migration reports for Stratozone (AWS & Azure) |
| `/inventory`   | POST   | Handles inventory management for AWS, Azure, and GCP |
| `/kube`        | POST   | Collects Kubernetes inventory from AWS (EKS) and GCP (GKE) |
| `/dfile`       | GET    | Downloads a specified file |
| `/logs`        | GET    | Retrieves logs |

### Endpoint Details

#### `/move_val`
Validates migration readiness using VM metadata.

**Request Parameters:**
- `move-group`: Move group for migration.
- `projid`: Project ID for cloud validation.
- `excel-file`: Input Excel file with VM details.

**Response:** Generates an Excel validation report.

#### `/m2vm`
Generates M2VM migration plans.

**Request Parameters:**
- `move-group`: Move group for migration.
- `source-name`: Source system name.
- `excel-file`: Input Excel file with VM details.

**Response:** CSV migration plan file.

#### `/gta_m2vm`
Processes GTA M2VM migration mappings.

**Request Parameters:**
- `move-group`: Move group for migration.
- `csv-file`: Exported migration CSV file.
- `excel-file`: Mapping file for configurations.

**Response:** CSV output with updated configurations.

#### `/vmware_m2vm`
Generates VMware migration reports.

**Request Parameters:**
- `excel-file`: VMware inventory Excel file.

**Response:** Excel file with VMware migration data.

#### `/buildsheet`
Generates cloud infrastructure build sheets.

**Request Parameters:**
- `excel-file`: Input Excel file.

**Response:** CSV build sheet file.

#### `/gcloudcmd`
Generates Google Cloud deployment commands.

**Request Parameters:**
- `excel-file`: CSV input file with VM details.

**Response:** Text file with Google Cloud commands.

#### `/xl_to_tfvars`
Converts Excel files into Terraform variable files.

**Request Parameters:**
- `excel-file`: Input Excel file.

**Response:** ZIP file containing `.tfvars` files.

#### `/inventory`
Handles cloud inventory management for AWS, Azure, and GCP.

**Request Parameters:**
- `call`: Cloud provider (`aws`, `azure`, `gcp`).
- `arn`: AWS ARN (Required for AWS).
- `regions`: AWS regions (Optional for AWS).
- `orgid`: GCP Organization ID (Required for GCP).
- `projids`: GCP Project IDs (Optional for GCP).
- `folderids`: GCP Folder IDs (Optional for GCP).
- `redis-reg`: GCP region (Optional for GCP).

**Response:** Excel inventory file.

#### `/kube`
Collects Kubernetes inventory for AWS (EKS) and GCP (GKE).

**Request Parameters:**
- `call`: Cloud provider (`aws` or `gcp`).
- `arn`: AWS ARN (Required for AWS).
- `projid`: GCP Project ID (Required for GCP).

**Response:** Kubernetes inventory file.

#### `/compliance`
Runs compliance checks for AWS, Azure, GCP, and VPCSC.

**Request Parameters:**
- `call`: Cloud provider (`aws`, `azure`, `gcp`, `vpcsc`).
- `arn`: AWS ARN (Required for AWS).
- `projid`: GCP Project ID (Required for GCP).
- `option`: Compliance benchmark to run.
- `uuid`: Unique identifier for report file.

**Response:** PDF compliance report.

## Logging & Debugging
- Logs are stored in `app.log`.
- Use `/logs` endpoint to retrieve logs.
- In development mode, run `python main.py debug` for detailed error messages.

## Environment Variables
| Variable    | Description |
|------------|-------------|
| SECRET_KEY | Secret key for Flask session management |
| PORT       | Port for running the application |
| ENVIRONMENT | `production` or `development` mode |

## Running in Different Modes
To start in production mode:
```sh
ENVIRONMENT=production ./start.sh
```
To start in development mode:
```sh
ENVIRONMENT=development ./start.sh
```

## Contributions
Feel free to contribute by submitting pull requests or reporting issues.

## License
This project is licensed under the MIT License.

